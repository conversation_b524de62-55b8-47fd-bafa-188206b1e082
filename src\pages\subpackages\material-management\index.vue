<template>
    <view>
        <view>
            <nut-menu>
                <nut-menu-item ref="searchBox" :title="t('ui.search')">
                    <nut-searchbar v-model="editSearch" :clearable="true" :placeholder="t('text.pleaseEnter')">
                        <template #leftin>
                            <Search2 />
                        </template>
                        <template #rightout>
                            <view style="display: flex; gap: 10px">
                                <nut-button type="primary" size="small" @click="search">{{
                                    t('ui.search')
                                }}</nut-button>
                                <nut-button type="primary" plain size="small" @click="resetSearch">{{
                                    t('ui.reset')
                                }}</nut-button>
                            </view>
                        </template>
                    </nut-searchbar>
                </nut-menu-item>
                <nut-menu-item ref="filtrate" :title="t('ui.filtrate')">
                    <view style="width: 100%; text-align: right; padding: 0 5px">
                        <nut-button type="primary" size="small" @click="clearFilter">{{
                            t('ui.clearFiltrate')
                        }}</nut-button>
                    </view>
                    <nut-cell
                        :title="t('text.selectionSort')"
                        :desc="defaultCategory.toString() || $t('text.pleaseSelectSort')"
                        @click="isShowCategory = true"
                    ></nut-cell>
                    <nut-cascader
                        v-model:visible="isShowCategory"
                        v-model="defaultCategory"
                        :title="t('text.selectionSort')"
                        :options="categoryList"
                        :convert-config="convertConfig"
                        @change="change"
                    ></nut-cascader>
                </nut-menu-item>
            </nut-menu>
        </view>
        <nut-row>
            <nut-col :span="24">
                <view
                    style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 0 20px;
                        margin-top: 2vh;
                    "
                >
                    <nut-button class="custom-btn primary-btn" plain type="info" size="small" @click="isAdd = true">
                        {{ t('text.add') }}
                    </nut-button>
                    <nut-button class="custom-btn outline-btn" plain type="info" size="small" @click="loading">
                        {{ t('ui.refresh') }}
                    </nut-button>
                </view>
            </nut-col>
        </nut-row>
        <view class="warehouse-list" v-if="totalList.length > 0">
            <view v-for="(item, index) in totalList" :key="index" @click="openDetails(item.id)">
                <InformationCard :isActivate="item.isActivate" @update:showMore="updateShowMore(index, $event)">
                    <template #front-title> [{{ index + 1 }}] </template>
                    <template #title>
                        {{ item.name }}
                    </template>
                    <template #line-first> {{ t('text.materialCode') }} ： {{ item.encode }} </template>
                    <template #line-second> {{ t('text.materialmodel') }} ： {{ item.model }} </template>
                    <template #content-default> {{ t('text.materialtype') }} ： {{ item.type }} </template>
                    <template #space-one>
                        <nut-tag :color="item.isActivate ? '#30D479' : '#FF0000'" style="margin-bottom: 0.5vh">
                            {{ $t(item.isActivate ? 'Helper.Enable' : 'Helper.Disable') }}
                        </nut-tag>
                    </template>
                    <template #space-two>
                        <view @click.stop>
                            <nut-popover
                                style="margin-left: 50px"
                                v-model:visible="showMore[index]"
                                :list="list"
                                theme="light"
                                location="left"
                                @choose="choose(item.id, $event)"
                            >
                                <template #reference>
                                    <nut-button plain type="info" size="small">{{ t('menu.more') }}</nut-button>
                                </template>
                            </nut-popover>
                        </view>
                    </template>
                </InformationCard>
            </view>
        </view>
        <view v-else class="empty-state">
            <nut-empty description="暂无数据" image="empty"> </nut-empty>
        </view>
        <view>
            <nut-dialog
                :title="t('text.warmReminder')"
                :content="t('text.isDelete')"
                v-model:visible="isDelete"
                @cancel="isDelete = false"
                @ok="confirmDel"
                :ok-text="t('ui.confirm')"
                :cancel-text="t('ui.cancel')"
            />
            <EditForm
                :isShow="isAdd"
                name="add"
                :categoryList="categoryList"
                @update:closeAdd="isAdd = $event"
                @update:fetch="fetchData"
            />
            <EditForm
                :isShow="isEdit"
                name="edit"
                :categoryList="categoryList"
                :editList="editList"
                @update:closeEdit="isEdit = $event"
                @update:fetch="fetchData"
            />
            <nut-popup position="bottom" v-model:visible="showRecycle" :style="{ height: '70%' }">
                <Recycle :recycleList="getRecycleList" @restore="handleRestore" />
            </nut-popup>
        </view>
    </view>
</template>

<script setup lang="ts">
import { WmMaterialService } from '@/api/proxy/enterprise/controller/wm-material.service'
import { GetWmMaterialInput, GetWmWarehouseInput, WmMaterialListDto } from '@/api/proxy/enterprise/wms/dtos'
import { useDidShow } from '@tarojs/taro'
import InformationCard from '@/components/InformationCard.vue'
import { nextTick, ref } from 'vue'
import { t } from '@/locale/fanyi'
import { Search2 } from '@nutui/icons-vue-taro'
import { WmCategoryService } from '@/api/proxy/enterprise/controller/wm-category.service'
import { NavigateTo } from '@/utils/Taro-api'
import { Loading, HideLoading } from '@/utils/Taro-api'
import EditForm from './material-details/component/material-edit-form.vue'
import Recycle from '@/pages/workbench/warehouse-management/component/warehouse-recycle-form.vue'

// 物料接口
interface MaterialItem {
    id: string
    name: string
    encode: string
    model: string
    type: string
    isActivate: boolean
    categoryId?: string
    measureUnitId?: string
}

// 分类项接口
interface CategoryItem {
    id?: string
    name?: string
    categoryId?: string
    children?: CategoryItem[]
}

// 扁平化后的分类项
interface FlatCategoryItem {
    value: string
    text: string
    id: string
    pid: string | null
}

// 分页请求参数接口
interface PagedRequestDto {
    skipCount: number
    maxResultCount: number
    filterText: string
    sorting: string
}

const wmMaterialService = new WmMaterialService()

const wmCategoryService = new WmCategoryService()

// 默认分页参数
const defaultParams: PagedRequestDto = {
    skipCount: 0,
    maxResultCount: 10,
    filterText: '',
    sorting: '',
}

const totalList = ref<MaterialItem[]>([])

const editSearch = ref<string>('')

const searchBox = ref()

const filtrate = ref()

const isShowCategory = ref(false)

const defaultCategory = ref([])

const categoryList = ref<FlatCategoryItem[]>([])

const editList = ref({})

const convertConfig = ref({
    topId: null,
    idKey: 'id',
    pidKey: 'pid',
    sortKey: '',
})

const originalList = ref<MaterialItem[]>([])

const isAdd = ref<boolean>(false)

const isEdit = ref<boolean>(false)

const showMore = ref<boolean[]>([])

const list = ref([{ name: t('menu.edit') }, { name: t('menu.delete') }])

const isDelete = ref<boolean>(false)

const currentId = ref<string>('')

const showRecycle = ref<boolean>(false)

const getRecycleList = ref<WmMaterialListDto[]>([])

const fetchData = async () => {
    const input: GetWmMaterialInput = {
        ...defaultParams,
        isDeleted: false,
    }
    try {
        const result = await wmMaterialService.getPaged(input)
        originalList.value = result.items
        totalList.value = result.items
        await fetchType()
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

const fetchType = async () => {
    // 物料类型映射表
    const typeMapping = {
        '0': '未定义',
        '1': '物料',
        '2': '产品',
        None: '未定义',
        Material: '物料',
        Product: '产品',
    }

    // 直接在前端进行类型转换，不再请求后端API
    totalList.value.forEach(item => {
        if (item.type) {
            // 根据类型值获取对应的文本
            item.type = typeMapping[item.type] || item.type
        }
    })
}

const fetchCategory = async () => {
    try {
        const result = await wmCategoryService.getTreeNodes('')
        const buildCategoryList = (items: CategoryItem[]) => {
            let flatList: FlatCategoryItem[] = [] // 用于存储扁平化的结果
            items.forEach(item => {
                // 只有当 id 和 name 都存在时才添加当前项
                if (item.id && item.name) {
                    flatList.push({
                        value: item.name,
                        text: item.name,
                        id: item.id,
                        pid: item.categoryId !== undefined ? item.categoryId : null,
                    })
                }
                // 如果有 children，则递归提取子项
                if (item.children && item.children.length > 0) {
                    flatList = flatList.concat(buildCategoryList(item.children))
                }
            })
            return flatList
        }
        categoryList.value = buildCategoryList(result)
    } catch (error: any) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

const fetchEditData = async (id: string) => {
    const input = {
        id: id,
    }
    try {
        const result = await wmMaterialService.getEditor(input)
        editList.value = result.wmMaterial
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

// 回收站列表数据接口
const fetchRecycleData = async (params: Partial<PagedRequestDto> = {}) => {
    const input: GetWmWarehouseInput = {
        ...defaultParams,
        ...params,
        isDeleted: true,
    }
    try {
        const result = await wmMaterialService.getPaged(input)
        getRecycleList.value = result.items.map((item: WmMaterialListDto) => {
            return {
                ...item,
                creationTime: item.creationTime ? formatDate(item.creationTime) : '',
            }
        })
    } catch (error) {
        console.error('获取数据错误:', error.response ? error.response.data : error.message)
    }
}

const clearFilter = () => {
    defaultCategory.value = []
    totalList.value = originalList.value
    filtrate.value.toggle()
}

const change = (...args: any) => {
    const selectedIds = args[1]
    totalList.value = originalList.value.filter(item =>
        selectedIds.some((selected: { id: string | undefined }) => selected.id === item.categoryId),
    )
    filtrate.value.toggle()
}

// 搜索函数
const search = async () => {
    if (editSearch.value.trim() === '') {
        totalList.value = originalList.value // 搜索为空时使用原始数据
    } else {
        // 在原始数据中搜索，而不是在 totalList 中搜索
        const filteredItems = originalList.value.filter(item =>
            item.name.toLowerCase().includes(editSearch.value.toLowerCase()),
        )
        totalList.value = filteredItems
    }
    searchBox.value.toggle()
}

// 重置搜索
const resetSearch = () => {
    editSearch.value = ''
    totalList.value = originalList.value
    searchBox.value.toggle()
}

// 刷新数据的函数
const loading = async () => {
    Loading()
    await fetchData()
    HideLoading()
}

// 打开回收站
const openRecycle = async () => {
    await fetchRecycleData()
    changeRecycle(true)
}

// 切换回收站显示状态
const changeRecycle = (value: boolean) => {
    showRecycle.value = value
}

const openDetails = (id: string) => {
    // 查找当前物料项以获取 measureUnitId
    const currentMaterial = totalList.value.find(item => item.id === id)
    // 如果存在 measureUnitId，则一并传递
    if (currentMaterial && currentMaterial.measureUnitId) {
        NavigateTo(
            `/pages/subpackages/material-management/material-details/index?id=${id}&measureUnitId=${currentMaterial.measureUnitId}`,
        )
    } else {
        NavigateTo(`/pages/subpackages/material-management/material-details/index?id=${id}`)
    }
}

// 格式化日期的函数
const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 更新 showMore 状态的函数
const updateShowMore = (index: number, value: boolean) => {
    showMore.value[index] = value // 更新数组中的状态
}

// 选择事件处理函数
const choose = async (id: string, event: { name: string }) => {
    if (event.name === t('menu.edit')) {
        await fetchEditData(id)
        isEdit.value = true
    } else if (event.name === t('menu.delete')) {
        isDelete.value = true
        currentId.value = id
    }
}

const confirmDel = async () => {
    await wmMaterialService.deleteById(currentId.value)
    await fetchData()
}

const handleRestore = async (id: string) => {
    try {
        await wmMaterialService.revokeDelete(id)
        await fetchRecycleData() // 刷新回收站列表
        await fetchData() // 刷新主列表
    } catch (error: any) {
        console.error('还原失败:', error.response ? error.response.data : error.message)
    }
}

useDidShow(async () => {
    Loading()
    await fetchData()
    await fetchCategory()
    HideLoading()
})
</script>

<style lang="scss">
.warehouse-list {
    padding: 20px 40px;
}
</style>
