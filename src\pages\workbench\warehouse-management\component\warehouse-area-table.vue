<template>
    <view v-if="warehouseAreaList.length === 0">
        <nut-empty :description="t('helper.noData')"></nut-empty>
    </view>
    <view v-else>
        <view style="margin-top: -20px; margin-bottom: 8px">
            <nut-row>
                <nut-col :span="12">
                    <nut-button type="info" plain size="small" @click="handleAdd">
                        {{ t('text.add') }}
                    </nut-button>
                </nut-col>
                <nut-col :span="12" style="text-align: right">
                    <nut-button type="info" plain size="small" @click="fetchData">
                        {{ t('ui.refresh') }}
                    </nut-button>
                </nut-col>
            </nut-row>
        </view>
        <InformationCard
            v-for="(item, index) in warehouseAreaList"
            :key="index"
            :show-empty-image="false"
            :isActivate="item.isActivate"
            @click:content="handleDetails(item.id)"
        >
            <template #front-title> [{{ index + 1 }}] </template>
            <template #title> {{ item.name }} </template>
            <template #line-first> {{ t('text.encode') }}: {{ item.encode }} </template>
            <template #line-second> {{ t('text.areaSize') }}: {{ item.areaSize }} </template>
            <template #line-third> {{ t('text.location') }}: {{ item.location }} </template>
            <template #line-fourth> {{ t('text.describe') }}: {{ item.describe }} </template>
            <template #space-one>
                <nut-tag size="small" :type="item.isActivate ? 'primary' : 'danger'">
                    {{ item.isActivate ? t('tag.enable') : t('tag.disable') }}
                </nut-tag>
            </template>
            <template #space-two>
                <view @click.stop>
                    <action-menu
                        :options="actionOptions"
                        @select="action => handleActionSelect(action, item)"
                        direction="left"
                    ></action-menu>
                </view>
            </template>
        </InformationCard>
        <nut-popup v-model:visible="editVisible" :style="{ padding: '16px', height: '85%', width: '92%' }" round>
            <Edit
                :editVisible="editVisible"
                @update:editVisible="editVisible = $event"
                :id="editId"
                @refresh="fetchData"
            />
        </nut-popup>
        <nut-popup v-model:visible="addVisible" :style="{ padding: '16px', height: '85%', width: '92%' }" round>
            <Add
                :addVisible="addVisible"
                @update:addVisible="addVisible = $event"
                :warehouseId="warehouseId"
                @refresh="fetchData"
            />
        </nut-popup>
        <nut-dialog
            :content="t('text.isDelete')"
            v-model:visible="deleteVisible"
            @cancel="handleDelete(false)"
            @ok="handleDelete(true)"
        />
    </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { WmWarehouseAreaService } from '@/api/proxy/enterprise/controller/wm-warehouse-area.service'
import { useDidShow } from '@tarojs/taro'
import InformationCard from '@/components/InformationCard.vue'
import { t } from '@/locale/fanyi'
import { WmWarehouseAreaListDto } from '@/api/proxy/enterprise/wms/dtos'
import Taro from '@tarojs/taro'
import ActionMenu from '@/components/ActionMenu'
import { ActionMenuItem } from '@/components/ActionMenu/models/ActionMenuDto'
import Edit from './warehouse-area-edit.vue'
import Add from './warehouse-area-add.vue'

Taro.setNavigationBarTitle({ title: t('menu.warehouseArea') })

const props = defineProps<{
    warehouseId?: string
}>()

const wmWarehouseAreaService = new WmWarehouseAreaService()
const warehouseId = ref(props.warehouseId)
const warehouseAreaList = ref<WmWarehouseAreaListDto[]>([])
const editVisible = ref(false)
const editId = ref('')
const addVisible = ref(false)
const deleteVisible = ref(false)
const deleteId = ref('')
const actionOptions: ActionMenuItem[] = [
    { text: t('menu.edit'), value: 'edit' },
    { text: t('menu.delete'), value: 'delete' },
]

const fetchData = async () => {
    try {
        if (warehouseId.value) {
            const result = await wmWarehouseAreaService.getPaged({
                warehouseId: warehouseId.value,
                isDeleted: false,
            })
            if (result) {
                warehouseAreaList.value = result.items
            }
        }
    } catch (error) {
        console.log(error)
    }
}

const handleDetails = (id: string) => {
    Taro.navigateTo({
        url: `/pages/workbench/warehouse-management/component/warehouse-area-details?id=${id}`,
    })
}

const handleDelete = async (isConfirm: boolean) => {
    try {
        if (isConfirm && deleteId.value) {
            await wmWarehouseAreaService.deleteById(deleteId.value)
            Taro.showToast({
                title: t('text.deleteSuccess'),
                icon: 'success',
            })
            await fetchData()
        }
        deleteVisible.value = false
    } catch (error) {
        Taro.showToast({
            title: t('text.deleteFail'),
            icon: 'error',
        })
    }
}

// 处理操作菜单选择
const handleActionSelect = async (action: string | number, item: WmWarehouseAreaListDto) => {
    switch (action) {
        case 'edit':
            editId.value = item.id
            editVisible.value = true
            break
        case 'delete':
            deleteId.value = item.id
            deleteVisible.value = true
            break
    }
}

const handleAdd = () => {
    addVisible.value = true
}

watch(
    () => props.warehouseId,
    newValue => {
        warehouseId.value = newValue
        if (newValue) {
            fetchData()
        }
    },
    { immediate: true },
)

useDidShow(async () => {
    if (warehouseId.value) {
        await fetchData()
    }
})
</script>

<style lang="scss"></style>
